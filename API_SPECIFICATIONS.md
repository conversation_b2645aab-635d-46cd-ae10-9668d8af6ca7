# BahinLink API Specifications

## ⚠️ CRITICAL REQUIREMENT: REAL DATA ONLY
**NO MOCK DATA ALLOWED**: All API endpoints must connect to real databases and external services. Every response must contain actual production data.

## Base URL
```
Production: https://api.bahinlink.com/v1
Development: http://localhost:3000/api/v1
```

## Authentication
All protected endpoints require JW<PERSON> token in Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Response Format
```json
{
  "success": true,
  "data": {},
  "message": "Success message",
  "timestamp": "2025-01-21T10:00:00Z"
}
```

## Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": {}
  },
  "timestamp": "2025-01-21T10:00:00Z"
}
```

## Authentication Endpoints

### POST /auth/login
Login user with credentials
```json
Request:
{
  "email": "<EMAIL>",
  "password": "password123"
}

Response:
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "agent",
      "firstName": "John",
      "lastName": "Doe"
    },
    "tokens": {
      "accessToken": "jwt_token",
      "refreshToken": "refresh_token",
      "expiresIn": 3600
    }
  }
}
```

### POST /auth/refresh
Refresh access token
```json
Request:
{
  "refreshToken": "refresh_token"
}

Response:
{
  "success": true,
  "data": {
    "accessToken": "new_jwt_token",
    "expiresIn": 3600
  }
}
```

### POST /auth/logout
Logout user (invalidate tokens)

## User Management

### GET /users
Get all users (admin only)
Query params: `role`, `page`, `limit`, `search`

### POST /users
Create new user
```json
Request:
{
  "username": "newagent",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "agent",
  "firstName": "Jane",
  "lastName": "Smith",
  "phone": "+**********"
}
```

### GET /users/:id
Get user by ID

### PUT /users/:id
Update user

### DELETE /users/:id
Delete user (admin only)

## Agent Management

### GET /agents
Get all agents
Query params: `available`, `skills`, `location`, `page`, `limit`

### POST /agents
Create agent profile
```json
Request:
{
  "userId": "user_uuid",
  "employeeId": "EMP001",
  "photoUrl": "https://s3.amazonaws.com/photos/agent1.jpg",
  "certifications": ["Security License", "First Aid"],
  "skills": ["Armed Security", "Patrol", "Access Control"],
  "availability": {
    "monday": {"start": "08:00", "end": "20:00"},
    "tuesday": {"start": "08:00", "end": "20:00"}
  },
  "emergencyContactName": "Emergency Contact",
  "emergencyContactPhone": "+**********",
  "hourlyRate": 25.00
}
```

### GET /agents/:id
Get agent details

### PUT /agents/:id
Update agent profile

### GET /agents/:id/location
Get current agent location
```json
Response:
{
  "success": true,
  "data": {
    "agentId": "uuid",
    "location": {
      "latitude": 40.7128,
      "longitude": -74.0060,
      "accuracy": 5.0,
      "timestamp": "2025-01-21T10:00:00Z"
    },
    "currentShift": {
      "id": "shift_uuid",
      "siteName": "Downtown Office",
      "status": "in_progress"
    }
  }
}
```

### POST /agents/:id/location
Update agent location (real GPS coordinates)
```json
Request:
{
  "latitude": 40.7128,
  "longitude": -74.0060,
  "accuracy": 5.0,
  "timestamp": "2025-01-21T10:00:00Z"
}
```

## Site Management

### GET /sites
Get all sites
Query params: `clientId`, `active`, `page`, `limit`

### POST /sites
Create new site
```json
Request:
{
  "clientId": "client_uuid",
  "name": "Downtown Office Building",
  "address": "123 Main St, New York, NY 10001",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "geofenceRadius": 100,
  "siteType": "office",
  "specialInstructions": "24/7 monitoring required",
  "accessCodes": {
    "main": "1234",
    "emergency": "9999"
  },
  "emergencyContacts": [
    {
      "name": "Building Manager",
      "phone": "+**********",
      "role": "Primary Contact"
    }
  ]
}
```

### GET /sites/:id
Get site details

### PUT /sites/:id
Update site

## Shift Management

### GET /shifts
Get shifts
Query params: `agentId`, `siteId`, `date`, `status`, `page`, `limit`

### POST /shifts
Create new shift
```json
Request:
{
  "siteId": "site_uuid",
  "agentId": "agent_uuid",
  "supervisorId": "supervisor_uuid",
  "shiftDate": "2025-01-21",
  "startTime": "08:00:00",
  "endTime": "16:00:00"
}
```

### GET /shifts/:id
Get shift details

### PUT /shifts/:id
Update shift

### GET /shifts/agent/:agentId
Get agent's shifts

### GET /shifts/site/:siteId
Get site's shifts

## Time Tracking

### POST /time/clock-in
Clock in to shift
```json
Request:
{
  "shiftId": "shift_uuid",
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060,
    "accuracy": 5.0
  },
  "method": "gps",
  "qrCode": "optional_qr_code"
}

Response:
{
  "success": true,
  "data": {
    "timeEntryId": "uuid",
    "clockInTime": "2025-01-21T08:00:00Z",
    "withinGeofence": true,
    "distanceFromSite": 15.5
  }
}
```

### POST /time/clock-out
Clock out from shift
```json
Request:
{
  "timeEntryId": "time_entry_uuid",
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060,
    "accuracy": 5.0
  },
  "method": "gps"
}
```

### GET /time/entries/:shiftId
Get time entries for shift

### PUT /time/entries/:id/verify
Verify time entry (supervisor only)

## Reporting

### GET /reports
Get reports
Query params: `type`, `agentId`, `siteId`, `status`, `dateFrom`, `dateTo`, `page`, `limit`

### POST /reports
Create new report
```json
Request:
{
  "type": "patrol",
  "shiftId": "shift_uuid",
  "siteId": "site_uuid",
  "title": "Evening Patrol Report",
  "description": "Routine patrol completed",
  "observations": "All areas secure, no incidents",
  "incidents": "",
  "actionsTaken": "Completed full perimeter check",
  "priority": "normal",
  "location": {
    "latitude": 40.7128,
    "longitude": -74.0060
  },
  "weatherConditions": "Clear, 72°F",
  "photos": ["https://s3.amazonaws.com/reports/photo1.jpg"],
  "videos": []
}
```

### GET /reports/:id
Get report details

### PUT /reports/:id
Update report

### POST /reports/:id/approve
Approve report (supervisor only)

### POST /reports/:id/reject
Reject report (supervisor only)
```json
Request:
{
  "reason": "Missing incident details"
}
```

### POST /reports/:id/signature
Add client signature
```json
Request:
{
  "signature": {
    "data": "base64_signature_image",
    "clientName": "John Client",
    "timestamp": "2025-01-21T10:00:00Z"
  }
}
```

## File Upload

### POST /upload/photo
Upload photo
```
Content-Type: multipart/form-data
File: photo file
```

### POST /upload/video
Upload video
```
Content-Type: multipart/form-data
File: video file
```

## Notifications

### GET /notifications
Get user notifications
Query params: `unread`, `priority`, `page`, `limit`

### POST /notifications
Send notification (admin/supervisor only)
```json
Request:
{
  "recipientId": "user_uuid",
  "type": "shift_assignment",
  "title": "New Shift Assigned",
  "message": "You have been assigned to Downtown Office on 2025-01-22",
  "priority": "normal",
  "data": {
    "shiftId": "shift_uuid"
  }
}
```

### PUT /notifications/:id/read
Mark notification as read

## Communications

### GET /communications
Get messages
Query params: `threadId`, `page`, `limit`

### POST /communications
Send message
```json
Request:
{
  "recipientId": "user_uuid",
  "messageType": "text",
  "content": "Please check the main entrance",
  "isUrgent": false
}
```

### PUT /communications/:id/read
Mark message as read

## Client Portal

### GET /client/sites
Get client's sites

### GET /client/agents
Get agents assigned to client sites

### GET /client/reports
Get reports for client sites

### POST /client/requests
Create service request
```json
Request:
{
  "siteId": "site_uuid",
  "requestType": "additional_patrol",
  "priority": "high",
  "title": "Extra Security Needed",
  "description": "Suspicious activity reported"
}
```

## Analytics & Dashboard

### GET /analytics/dashboard
Get dashboard data
```json
Response:
{
  "success": true,
  "data": {
    "activeAgents": 25,
    "activeShifts": 12,
    "pendingReports": 3,
    "clientSatisfaction": 4.8,
    "recentIncidents": 2,
    "monthlyStats": {
      "totalHours": 2400,
      "completedShifts": 180,
      "reportSubmissions": 156
    }
  }
}
```

### GET /analytics/performance
Get performance metrics
Query params: `agentId`, `siteId`, `dateFrom`, `dateTo`

## Real-time Events (WebSocket)

### Connection
```javascript
const socket = io('wss://api.bahinlink.com', {
  auth: {
    token: 'jwt_token'
  }
});
```

### Events
- `location-update`: Real-time agent location updates
- `notification-new`: New notifications
- `shift-status-change`: Shift status changes
- `report-submitted`: New report submissions
- `emergency-alert`: Emergency alerts
- `geofence-violation`: Geofence violations

## Rate Limiting
- Authentication: 5 requests per minute
- General API: 100 requests per minute
- File uploads: 10 requests per minute
- Location updates: 60 requests per minute

## Error Codes
- `AUTH_REQUIRED`: Authentication required
- `AUTH_INVALID`: Invalid credentials
- `AUTH_EXPIRED`: Token expired
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `VALIDATION_ERROR`: Input validation failed
- `GEOFENCE_VIOLATION`: Location outside allowed area
- `SHIFT_CONFLICT`: Scheduling conflict
- `FILE_TOO_LARGE`: File size exceeds limit
