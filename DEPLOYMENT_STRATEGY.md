# BahinLink Deployment Strategy

## ⚠️ CRITICAL REQUIREMENT: REAL PRODUCTION ENVIRONMENT
**NO MOCK SERVICES**: All deployment must use real production services, real databases, real authentication, and real integrations from day one.

## Production Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Mobile App    │    │   Web Admin     │    │  Client Portal  │
│  (React Native) │    │    (React)      │    │    (React)      │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │      Load Balancer      │
                    │      (AWS ALB)          │
                    └─────────────┬───────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │    API Server Cluster   │
                    │   (ECS/Docker + Node.js)│
                    └─────────────┬───────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
┌─────────┴───────┐    ┌─────────┴───────┐    ┌─────────┴───────┐
│   PostgreSQL    │    │   Redis Cache   │    │   AWS S3        │
│   (RDS)         │    │   (ElastiCache) │    │   (File Storage)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Infrastructure Requirements

### 1. Cloud Provider: AWS
- **Compute**: ECS Fargate for containerized API
- **Database**: RDS PostgreSQL (Multi-AZ for HA)
- **Cache**: ElastiCache Redis
- **Storage**: S3 for files, CloudFront CDN
- **Load Balancer**: Application Load Balancer
- **Monitoring**: CloudWatch + X-Ray
- **Security**: WAF, VPC, Security Groups

### 2. External Services (Real Production)
- **Authentication**: Clerk (Production plan)
- **Maps**: Google Maps API (Production quota)
- **Push Notifications**: Firebase Cloud Messaging
- **Email**: SendGrid (Production plan)
- **SMS**: Twilio (Production account)
- **Monitoring**: Sentry for error tracking

## Environment Configuration

### Production Environment Variables
```env
# Database (Real RDS PostgreSQL)
DATABASE_URL="postgresql://bahinlink:<EMAIL>:5432/bahinlink"

# Clerk Authentication (Real Production Keys)
CLERK_PUBLISHABLE_KEY="pk_live_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
CLERK_SECRET_KEY="sk_live_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
CLERK_WEBHOOK_SECRET="whsec_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# AWS Services (Real Production Credentials)
AWS_ACCESS_KEY_ID="AKIAXXXXXXXXXXXXXXXX"
AWS_SECRET_ACCESS_KEY="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="bahinlink-production-files"

# Google Maps (Real Production API Key)
GOOGLE_MAPS_API_KEY="AIzaxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Firebase (Real Production Config)
FIREBASE_SERVER_KEY="AAAAxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
FIREBASE_PROJECT_ID="bahinlink-production"

# Communication Services (Real Production)
SENDGRID_API_KEY="SG.xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
TWILIO_ACCOUNT_SID="ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"
TWILIO_AUTH_TOKEN="xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx"

# Redis Cache (Real ElastiCache)
REDIS_URL="redis://bahinlink-prod.cache.amazonaws.com:6379"

# Application Settings
NODE_ENV="production"
PORT="3000"
API_BASE_URL="https://api.bahinlink.com"
WEB_BASE_URL="https://admin.bahinlink.com"
CLIENT_BASE_URL="https://client.bahinlink.com"

# Security
JWT_SECRET="production_jwt_secret_very_secure_random_string"
ENCRYPTION_KEY="production_encryption_key_32_chars"
```

## Deployment Steps

### Phase 1: Infrastructure Setup (15 minutes)

#### 1.1 AWS Infrastructure (10 minutes)
```bash
# Create VPC and networking
aws ec2 create-vpc --cidr-block 10.0.0.0/16
aws ec2 create-subnet --vpc-id vpc-xxx --cidr-block 10.0.1.0/24
aws ec2 create-subnet --vpc-id vpc-xxx --cidr-block 10.0.2.0/24

# Create RDS PostgreSQL instance
aws rds create-db-instance \
  --db-instance-identifier bahinlink-prod \
  --db-instance-class db.t3.medium \
  --engine postgres \
  --engine-version 15.4 \
  --master-username bahinlink \
  --master-user-password SecurePassword123! \
  --allocated-storage 100 \
  --storage-type gp2 \
  --multi-az \
  --backup-retention-period 7

# Create ElastiCache Redis
aws elasticache create-cache-cluster \
  --cache-cluster-id bahinlink-prod \
  --cache-node-type cache.t3.micro \
  --engine redis \
  --num-cache-nodes 1

# Create S3 bucket
aws s3 mb s3://bahinlink-production-files
aws s3api put-bucket-versioning \
  --bucket bahinlink-production-files \
  --versioning-configuration Status=Enabled
```

#### 1.2 External Services Setup (5 minutes)
```bash
# Clerk Production Setup
# 1. Upgrade to Clerk Production plan
# 2. Configure production domain: api.bahinlink.com
# 3. Set up webhooks: https://api.bahinlink.com/webhooks/clerk
# 4. Configure user roles and metadata

# Google Maps API
# 1. Enable Maps JavaScript API, Geocoding API, Places API
# 2. Set up billing account
# 3. Configure API key restrictions

# Firebase Setup
# 1. Create production Firebase project
# 2. Enable Cloud Messaging
# 3. Generate server key
# 4. Configure Android/iOS apps
```

### Phase 2: Database Migration (10 minutes)

#### 2.1 Prisma Production Setup
```bash
# Set production database URL
export DATABASE_URL="*****************************************************************************************************/bahinlink"

# Run migrations
npx prisma migrate deploy

# Generate Prisma client
npx prisma generate

# Seed initial data (admin user, sample sites)
npx prisma db seed
```

#### 2.2 Database Optimization
```sql
-- Create production indexes
CREATE INDEX CONCURRENTLY idx_agents_location ON agents USING GIST(ST_Point(current_longitude, current_latitude));
CREATE INDEX CONCURRENTLY idx_time_entries_performance ON time_entries(agent_id, clock_in_time) WHERE clock_in_time IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_reports_search ON reports USING GIN(to_tsvector('english', title || ' ' || description));

-- Set up connection pooling
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
```

### Phase 3: API Deployment (15 minutes)

#### 3.1 Docker Container Build
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY prisma ./prisma/

# Install dependencies
RUN npm ci --only=production

# Generate Prisma client
RUN npx prisma generate

# Copy application code
COPY . .

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["npm", "start"]
```

#### 3.2 ECS Deployment
```json
{
  "family": "bahinlink-api",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::account:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::account:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "bahinlink-api",
      "image": "your-account.dkr.ecr.us-east-1.amazonaws.com/bahinlink-api:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "NODE_ENV",
          "value": "production"
        }
      ],
      "secrets": [
        {
          "name": "DATABASE_URL",
          "valueFrom": "arn:aws:secretsmanager:us-east-1:account:secret:bahinlink/database-url"
        },
        {
          "name": "CLERK_SECRET_KEY",
          "valueFrom": "arn:aws:secretsmanager:us-east-1:account:secret:bahinlink/clerk-secret"
        }
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/bahinlink-api",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### Phase 4: Frontend Deployment (15 minutes)

#### 4.1 Mobile App Build
```bash
# Android Production Build
cd BahinLinkApp
npx react-native bundle --platform android --dev false --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle

# Generate signed APK
cd android
./gradlew assembleRelease

# Upload to Google Play Console (internal testing first)
```

#### 4.2 Web Admin Deployment
```bash
# Build React admin app
cd bahinlink-admin
npm run build

# Deploy to S3 + CloudFront
aws s3 sync build/ s3://bahinlink-admin-production
aws cloudfront create-invalidation --distribution-id E123456789 --paths "/*"
```

#### 4.3 Client Portal Deployment
```bash
# Build React client portal
cd bahinlink-client
npm run build

# Deploy to S3 + CloudFront
aws s3 sync build/ s3://bahinlink-client-production
aws cloudfront create-invalidation --distribution-id E987654321 --paths "/*"
```

### Phase 5: Monitoring & Security (5 minutes)

#### 5.1 Monitoring Setup
```bash
# CloudWatch alarms
aws cloudwatch put-metric-alarm \
  --alarm-name "BahinLink-API-HighCPU" \
  --alarm-description "API CPU utilization is too high" \
  --metric-name CPUUtilization \
  --namespace AWS/ECS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold

# Sentry error tracking
npm install @sentry/node
# Configure in production
```

#### 5.2 Security Configuration
```bash
# WAF rules
aws wafv2 create-web-acl \
  --name BahinLink-WAF \
  --scope CLOUDFRONT \
  --default-action Allow={} \
  --rules file://waf-rules.json

# SSL certificates
aws acm request-certificate \
  --domain-name api.bahinlink.com \
  --domain-name admin.bahinlink.com \
  --domain-name client.bahinlink.com \
  --validation-method DNS
```

## Production Checklist

### Pre-Deployment
- [ ] All environment variables configured with real production values
- [ ] Database migrations tested and ready
- [ ] Clerk production account configured
- [ ] Google Maps API quota sufficient for production load
- [ ] AWS infrastructure provisioned
- [ ] SSL certificates issued
- [ ] Domain names configured

### Post-Deployment
- [ ] Health checks passing
- [ ] Real-time features working (WebSocket connections)
- [ ] GPS tracking functional with real coordinates
- [ ] File uploads working to S3
- [ ] Push notifications sending via Firebase
- [ ] Email notifications via SendGrid
- [ ] Database performance optimized
- [ ] Monitoring and alerting active
- [ ] Security scans completed

### Performance Targets
- API response time: < 200ms (95th percentile)
- Database query time: < 50ms (average)
- Mobile app startup: < 3 seconds
- Real-time location updates: < 1 second latency
- File upload: < 10 seconds for 10MB files
- Uptime: 99.9% availability

## Rollback Strategy

### Emergency Rollback
```bash
# Rollback ECS service to previous task definition
aws ecs update-service \
  --cluster bahinlink-prod \
  --service bahinlink-api \
  --task-definition bahinlink-api:previous-revision

# Rollback database migration (if needed)
npx prisma migrate reset --force
npx prisma migrate deploy --to migration-id

# Rollback frontend deployments
aws s3 sync s3://bahinlink-admin-backup/ s3://bahinlink-admin-production/
```

## Scaling Strategy

### Auto Scaling Configuration
```json
{
  "targetTrackingScalingPolicies": [
    {
      "targetValue": 70.0,
      "predefinedMetricSpecification": {
        "predefinedMetricType": "ECSServiceAverageCPUUtilization"
      }
    }
  ],
  "minCapacity": 2,
  "maxCapacity": 10
}
```

This deployment strategy ensures a robust, scalable, and secure production environment using real services and infrastructure from day one.
