-- BahinLink Database Schema
-- ⚠️ CRITICAL: This schema is designed for REAL PRODUCTION DATA ONLY
-- NO MOCK DATA - All tables and relationships must handle actual business data

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Users table (all user types: admin, supervisor, agent, client)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL CHECK (role IN ('admin', 'supervisor', 'agent', 'client')),
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT true,
    last_login TIMES<PERSON><PERSON>,
    password_reset_token VARCHAR(255),
    password_reset_expires TIMES<PERSON><PERSON>,
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_secret VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Agents table (security personnel details)
CREATE TABLE agents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    employee_id VARCHAR(50) UNIQUE NOT NULL,
    photo_url VARCHAR(500),
    certifications JSONB DEFAULT '[]'::jsonb,
    skills JSONB DEFAULT '[]'::jsonb,
    availability JSONB DEFAULT '{}'::jsonb,
    performance_stats JSONB DEFAULT '{}'::jsonb,
    emergency_contact_name VARCHAR(255),
    emergency_contact_phone VARCHAR(20),
    hire_date DATE,
    hourly_rate DECIMAL(10,2),
    is_available BOOLEAN DEFAULT true,
    current_location GEOMETRY(POINT, 4326),
    last_location_update TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Clients table (companies using security services)
CREATE TABLE clients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    company_name VARCHAR(255) NOT NULL,
    contact_person VARCHAR(255),
    billing_address TEXT,
    service_level VARCHAR(50) DEFAULT 'standard',
    contract_start_date DATE,
    contract_end_date DATE,
    billing_cycle VARCHAR(20) DEFAULT 'monthly',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sites table (client locations requiring security)
CREATE TABLE sites (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    location GEOMETRY(POINT, 4326) NOT NULL,
    geofence_radius INTEGER DEFAULT 100, -- meters
    qr_code VARCHAR(255) UNIQUE,
    site_type VARCHAR(50),
    special_instructions TEXT,
    access_codes JSONB DEFAULT '{}'::jsonb,
    emergency_contacts JSONB DEFAULT '[]'::jsonb,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Shifts table (scheduled work periods)
CREATE TABLE shifts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE SET NULL,
    supervisor_id UUID REFERENCES users(id) ON DELETE SET NULL,
    shift_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    status VARCHAR(20) DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'in_progress', 'completed', 'cancelled', 'no_show')),
    actual_start_time TIMESTAMP,
    actual_end_time TIMESTAMP,
    break_duration INTEGER DEFAULT 0, -- minutes
    overtime_hours DECIMAL(5,2) DEFAULT 0,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Time tracking table (clock in/out records)
CREATE TABLE time_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    shift_id UUID REFERENCES shifts(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    clock_in_time TIMESTAMP,
    clock_out_time TIMESTAMP,
    clock_in_location GEOMETRY(POINT, 4326),
    clock_out_location GEOMETRY(POINT, 4326),
    clock_in_method VARCHAR(20) CHECK (clock_in_method IN ('gps', 'qr_code', 'manual', 'nfc')),
    clock_out_method VARCHAR(20) CHECK (clock_out_method IN ('gps', 'qr_code', 'manual', 'nfc')),
    clock_in_accuracy DECIMAL(8,2), -- GPS accuracy in meters
    clock_out_accuracy DECIMAL(8,2),
    total_hours DECIMAL(5,2),
    is_verified BOOLEAN DEFAULT false,
    verified_by UUID REFERENCES users(id),
    verified_at TIMESTAMP,
    discrepancy_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Reports table (patrol and incident reports)
CREATE TABLE reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type VARCHAR(20) NOT NULL CHECK (type IN ('patrol', 'incident', 'inspection', 'maintenance')),
    shift_id UUID REFERENCES shifts(id) ON DELETE CASCADE,
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    supervisor_id UUID REFERENCES users(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    observations TEXT,
    incidents TEXT,
    actions_taken TEXT,
    recommendations TEXT,
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'submitted', 'under_review', 'approved', 'rejected', 'archived')),
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'critical')),
    client_signature JSONB,
    client_feedback TEXT,
    photos JSONB DEFAULT '[]'::jsonb, -- array of photo URLs
    videos JSONB DEFAULT '[]'::jsonb, -- array of video URLs
    attachments JSONB DEFAULT '[]'::jsonb,
    location GEOMETRY(POINT, 4326),
    weather_conditions VARCHAR(100),
    temperature DECIMAL(5,2),
    submitted_at TIMESTAMP,
    approved_at TIMESTAMP,
    rejected_at TIMESTAMP,
    rejection_reason TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Notifications table (system notifications)
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    sender_id UUID REFERENCES users(id) ON DELETE SET NULL,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB DEFAULT '{}'::jsonb,
    is_read BOOLEAN DEFAULT false,
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent', 'emergency')),
    delivery_method VARCHAR(20) DEFAULT 'app' CHECK (delivery_method IN ('app', 'email', 'sms', 'push')),
    scheduled_for TIMESTAMP,
    delivered_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP
);

-- Communication logs table (internal messaging)
CREATE TABLE communications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sender_id UUID REFERENCES users(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    thread_id UUID,
    message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file', 'voice', 'video')),
    content TEXT NOT NULL,
    attachments JSONB DEFAULT '[]'::jsonb,
    is_read BOOLEAN DEFAULT false,
    is_urgent BOOLEAN DEFAULT false,
    reply_to UUID REFERENCES communications(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP,
    edited_at TIMESTAMP
);

-- Audit logs table (system activity tracking)
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    table_name VARCHAR(50),
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    api_endpoint VARCHAR(255),
    http_method VARCHAR(10),
    response_status INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Client requests table (service requests from clients)
CREATE TABLE client_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    request_type VARCHAR(50) NOT NULL,
    priority VARCHAR(20) DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent', 'emergency')),
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'acknowledged', 'in_progress', 'resolved', 'closed', 'cancelled')),
    assigned_to UUID REFERENCES users(id) ON DELETE SET NULL,
    estimated_completion TIMESTAMP,
    actual_completion TIMESTAMP,
    client_satisfaction_rating INTEGER CHECK (client_satisfaction_rating BETWEEN 1 AND 5),
    client_feedback TEXT,
    internal_notes TEXT,
    cost_estimate DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP
);

-- Geofence violations table (tracking location violations)
CREATE TABLE geofence_violations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID REFERENCES agents(id) ON DELETE CASCADE,
    site_id UUID REFERENCES sites(id) ON DELETE CASCADE,
    shift_id UUID REFERENCES shifts(id) ON DELETE SET NULL,
    violation_type VARCHAR(50) NOT NULL,
    agent_location GEOMETRY(POINT, 4326) NOT NULL,
    distance_from_site DECIMAL(10,2),
    duration_minutes INTEGER,
    is_resolved BOOLEAN DEFAULT false,
    resolution_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP
);

-- Performance indexes for optimal query performance
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);
CREATE INDEX idx_agents_user_id ON agents(user_id);
CREATE INDEX idx_agents_employee_id ON agents(employee_id);
CREATE INDEX idx_agents_available ON agents(is_available);
CREATE INDEX idx_shifts_date_agent ON shifts(shift_date, agent_id);
CREATE INDEX idx_shifts_site_date ON shifts(site_id, shift_date);
CREATE INDEX idx_shifts_status ON shifts(status);
CREATE INDEX idx_time_entries_shift ON time_entries(shift_id);
CREATE INDEX idx_time_entries_agent_date ON time_entries(agent_id, clock_in_time);
CREATE INDEX idx_reports_agent_date ON reports(agent_id, created_at);
CREATE INDEX idx_reports_site_status ON reports(site_id, status);
CREATE INDEX idx_reports_type_date ON reports(type, created_at);
CREATE INDEX idx_notifications_recipient_unread ON notifications(recipient_id, is_read);
CREATE INDEX idx_notifications_priority ON notifications(priority);
CREATE INDEX idx_communications_sender ON communications(sender_id);
CREATE INDEX idx_communications_recipient ON communications(recipient_id);
CREATE INDEX idx_audit_logs_user_date ON audit_logs(user_id, created_at);
CREATE INDEX idx_audit_logs_table_record ON audit_logs(table_name, record_id);
CREATE INDEX idx_client_requests_client_status ON client_requests(client_id, status);
CREATE INDEX idx_geofence_violations_agent ON geofence_violations(agent_id);

-- Spatial indexes for location-based queries
CREATE INDEX idx_agents_location ON agents USING GIST(current_location);
CREATE INDEX idx_sites_location ON sites USING GIST(location);
CREATE INDEX idx_time_entries_clock_in_location ON time_entries USING GIST(clock_in_location);
CREATE INDEX idx_reports_location ON reports USING GIST(location);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_agents_updated_at BEFORE UPDATE ON agents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sites_updated_at BEFORE UPDATE ON sites FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_shifts_updated_at BEFORE UPDATE ON shifts FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_time_entries_updated_at BEFORE UPDATE ON time_entries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_reports_updated_at BEFORE UPDATE ON reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_client_requests_updated_at BEFORE UPDATE ON client_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Views for common queries
CREATE VIEW active_shifts AS
SELECT 
    s.*,
    a.first_name || ' ' || a.last_name as agent_name,
    st.name as site_name,
    c.company_name as client_name
FROM shifts s
JOIN agents ag ON s.agent_id = ag.id
JOIN users a ON ag.user_id = a.id
JOIN sites st ON s.site_id = st.id
JOIN clients c ON st.client_id = c.id
WHERE s.status IN ('scheduled', 'in_progress');

CREATE VIEW current_agent_locations AS
SELECT 
    a.id as agent_id,
    u.first_name || ' ' || u.last_name as agent_name,
    a.current_location,
    a.last_location_update,
    s.id as current_shift_id,
    st.name as current_site_name
FROM agents a
JOIN users u ON a.user_id = u.id
LEFT JOIN shifts s ON a.id = s.agent_id 
    AND s.status = 'in_progress' 
    AND s.shift_date = CURRENT_DATE
LEFT JOIN sites st ON s.site_id = st.id
WHERE a.is_available = true AND u.is_active = true;

-- Initial admin user (password: admin123 - CHANGE IN PRODUCTION)
INSERT INTO users (username, email, password_hash, role, first_name, last_name) 
VALUES ('admin', '<EMAIL>', '$2b$10$rQZ8kqVZ8qVZ8qVZ8qVZ8O', 'admin', 'System', 'Administrator');
